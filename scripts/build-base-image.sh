#!/bin/bash

# 构建本地基础镜像脚本
# 用于创建项目专用的nginx基础镜像

set -e

echo "🚀 开始构建本地基础镜像..."

# 定义镜像名称和标签
BASE_IMAGE_NAME="income-fe-nginx"
BASE_IMAGE_TAG="1.29-alpine"
LOCAL_IMAGE_TAG="income-fe-nginx:base"

echo "📦 拉取nginx基础镜像: nginx:${BASE_IMAGE_TAG}"
docker pull nginx:${BASE_IMAGE_TAG}

echo "🏷️  为基础镜像打上本地标签: ${LOCAL_IMAGE_TAG}"
docker tag nginx:${BASE_IMAGE_TAG} ${LOCAL_IMAGE_TAG}

echo "📋 显示本地镜像列表:"
docker images | grep -E "(nginx|income-fe)"

echo "✅ 本地基础镜像构建完成!"
echo "💡 现在可以在Dockerfile中使用: FROM ${LOCAL_IMAGE_TAG}"

# 可选：创建自定义的基础镜像（包含一些预配置）
read -p "是否要创建自定义的基础镜像？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔧 创建自定义基础镜像..."
    
    # 创建临时Dockerfile用于自定义基础镜像
    cat > Dockerfile.base << EOF
FROM nginx:${BASE_IMAGE_TAG}

# 添加自定义配置
LABEL maintainer="income-fe-team"
LABEL version="1.0"
LABEL description="Income FE custom nginx base image"

# 创建必要的目录
RUN mkdir -p /var/log/nginx /var/cache/nginx

# 设置时区
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 优化nginx配置
RUN sed -i 's/worker_processes  1/worker_processes  auto/' /etc/nginx/nginx.conf

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
EOF

    echo "🏗️  构建自定义基础镜像..."
    docker build -f Dockerfile.base -t income-fe-nginx:custom .
    
    echo "🧹 清理临时文件..."
    rm Dockerfile.base
    
    echo "✅ 自定义基础镜像构建完成: income-fe-nginx:custom"
fi

echo "🎉 所有操作完成!"
